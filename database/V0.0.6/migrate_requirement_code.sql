-- 迁移脚本：移除 requirement_code 字段的唯一约束并允许为 null
-- 版本：V0.0.2
-- 日期：2025-05-29
-- 描述：修复需求编号生成逻辑，移除全局唯一约束，允许在项目范围内重复

-- SQLite 不支持直接删除约束，需要重建表
-- 1. 创建新的临时表结构（没有唯一约束，允许 null）
CREATE TABLE requirements_new (
    id INTEGER NOT NULL, 
    project_id INTEGER NOT NULL, 
    requirement_code VARCHAR(10), 
    title VARCHAR(100) NOT NULL, 
    content TEXT NOT NULL, 
    type VARCHAR(11) NOT NULL, 
    priority VARCHAR(2) NOT NULL, 
    status VARCHAR(10) NOT NULL, 
    start_date DATETIME NOT NULL, 
    end_date DATETIME NOT NULL, 
    submitter_id INTEGER NOT NULL, 
    submit_time DATETIME NOT NULL, 
    update_time DATETIME, 
    git_branch VARCHAR(200), 
    PRIMARY KEY (id), 
    FOREIGN KEY(project_id) REFERENCES projects (id), 
    FOREIGN KEY(submitter_id) REFERENCES users (id)
);

-- 2. 复制现有数据到新表
INSERT INTO requirements_new (
    id, project_id, requirement_code, title, content, type, priority, status, 
    start_date, end_date, submitter_id, submit_time, update_time, git_branch
)
SELECT 
    id, project_id, requirement_code, title, content, type, priority, status, 
    start_date, end_date, submitter_id, submit_time, update_time, git_branch
FROM requirements;

-- 3. 删除原表
DROP TABLE requirements;

-- 4. 重命名新表
ALTER TABLE requirements_new RENAME TO requirements;

-- 5. 重建索引（如果需要的话）
-- CREATE INDEX idx_requirements_project_id ON requirements(project_id);
-- CREATE INDEX idx_requirements_submitter_id ON requirements(submitter_id);
-- CREATE INDEX idx_requirements_status ON requirements(status);
