import logging
from fastapi import Depends, APIRouter, HTTPException, status, Query, Body
from sqlalchemy.orm import Session, aliased
from typing import List, Optional, Set
# 使用 get_utc_now 函数替代 datetime.now(timezone.utc)
from sqlalchemy import desc, func

from app.core.database import get_db
from app.models.requirement import (
    Requirement as RequirementModel,
    RequirementAssignment,
    RequirementHistory,
    RequirementBranch,
    RequirementPullRequest,
    RequirementType,
    RequirementStatus,
    RequirementAction,
    PullRequestStatus,
    get_utc_now
)
from app.models.user import User
from app.models.project import Project, project_testers_table
from app.schemas.requirement import (
    RequirementCreate,
    RequirementDraftCreate,
    RequirementUpdate,
    RequirementResponse,
    RequirementHistoryCreate,
    RequirementAction as SchemaRequirementAction
)
from app.core.security import get_current_user
from app.utils.ai_service import ai_service
from app.utils.gitlab_api import gitlab_api
from app.services.notification_service import NotificationService

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/requirements", response_model=RequirementResponse)
def create_requirement(
    requirement: RequirementCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    创建新需求
    """
    # 验证项目是否存在
    project = db.query(Project).filter(Project.id == requirement.project_id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    logger.info(f"[需求创建] 开始处理需求: '{requirement.title}'")
    logger.info(f"[需求创建] 项目ID: {project.id}, 项目名称: {project.name}")

    # 根据is_draft字段决定状态
    initial_status = RequirementStatus.DRAFT if getattr(requirement, 'is_draft', False) else RequirementStatus.PENDING
    action_code = RequirementAction.SAVE_DRAFT if initial_status == RequirementStatus.DRAFT else RequirementAction.SUBMIT

    # 只有在非草稿状态下才生成分支名称和创建GitLab分支
    gitlab_branch_name = None
    if initial_status != RequirementStatus.DRAFT:
        # 调用AI服务将需求标题转换为英文分支名称
        try:
            logger.info(f"[需求创建] 调用AI服务翻译需求标题")
            branch_name = ai_service.translate_title_to_branch_name(requirement.title)
            logger.info(f"[需求创建] AI成功生成分支名称: '{branch_name}'")

            # 验证生成的分支名是否为英文
            if any(ord(c) > 127 for c in branch_name):
                logger.warning(f"[需求创建] 警告：AI生成的分支名称包含非ASCII字符: '{branch_name}'")
                # 如果包含非ASCII字符，使用备用方法
                branch_name = requirement.title.lower().replace(" ", "-").replace("，", "-").replace("。", "").replace("：", "-")
                # 移除所有非ASCII字符
                branch_name = ''.join(c for c in branch_name if ord(c) < 128)
                logger.info(f"[需求创建] 使用备用方法生成的分支名称: '{branch_name}'")
        except Exception as e:
            logger.error(f"[需求创建] 调用AI服务失败: {str(e)}")
            # 如果AI服务调用失败，使用一个简单的转换方法
            branch_name = requirement.title.lower().replace(" ", "-").replace("，", "-").replace("。", "").replace("：", "-")
            # 移除所有非ASCII字符
            branch_name = ''.join(c for c in branch_name if ord(c) < 128)
            logger.info(f"[需求创建] 使用备用方法生成的分支名称: '{branch_name}'")

        # 根据需求类型选择分支前缀
        if requirement.type == RequirementType.NEW_FEATURE:
            branch_prefix = "feature"
        elif requirement.type == RequirementType.BUG_FIX:
            branch_prefix = "bugfix"
        elif requirement.type == RequirementType.HOT_FIX_BUG:
            branch_prefix = "hotfix"
        else:
            branch_prefix = "feature"  # 默认使用feature前缀

        # 构建分支名称：前缀/英文分支名
        gitlab_branch_name = f"{branch_prefix}/{branch_name}"
        logger.info(f"[GitLab] 准备创建GitLab分支: {gitlab_branch_name}, 源分支: {requirement.main_branch}")

        # 创建GitLab分支
        try:
            # 调用GitLab API创建分支
            result = gitlab_api.create_branch(
                project_id=project.id,
                branch_name=gitlab_branch_name,
                ref=requirement.main_branch
            )
            logger.info(f"[GitLab] 分支创建成功: {gitlab_branch_name}")
            logger.info(f"[GitLab] 创建结果: {result}")
        except Exception as e:
            logger.error(f"[GitLab] 创建分支失败: {str(e)}")
            gitlab_branch_name = None  # 如果创建失败，设置为None
            # 分支创建失败不阻止需求创建，只记录错误日志
    else:
        logger.info(f"[需求创建] 草稿状态，跳过分支创建")

    # 创建需求记录
    db_requirement = RequirementModel(
        project_id=requirement.project_id,
        requirement_code=None,  # 不生成需求编号
        title=requirement.title,
        content=requirement.content,
        type=requirement.type,
        priority=requirement.priority,
        status=initial_status,
        start_date=requirement.start_date,
        end_date=requirement.end_date,
        submitter_id=current_user.id,
        submit_time=get_utc_now(),
        git_branch=gitlab_branch_name if initial_status != RequirementStatus.DRAFT else None  # 草稿状态不创建分支
    )
    db.add(db_requirement)
    db.flush()  # 获取自增ID

    # 添加需求历史记录
    history = RequirementHistory(
        requirement_id=db_requirement.id,
        current_status_code=initial_status.name,
        action_code=action_code.name,
        user_id=current_user.id,
        action_time=get_utc_now()
    )
    db.add(history)

    # 添加开发人员关联
    for dev_id in requirement.developer_ids:
        assignment = RequirementAssignment(
            requirement_id=db_requirement.id,
            user_id=dev_id
        )
        db.add(assignment)

    # 只有在非草稿状态下才添加分支关联
    if initial_status != RequirementStatus.DRAFT:
        # 添加主目标分支
        main_branch_record = RequirementBranch(
            requirement_id=db_requirement.id,
            branch_name=requirement.main_branch,
            is_main=True
        )
        db.add(main_branch_record)

        # 添加其他目标分支
        for branch in requirement.other_branches:
            branch_record = RequirementBranch(
                requirement_id=db_requirement.id,
                branch_name=branch,
                is_main=False
            )
            db.add(branch_record)

    db.commit()
    db.refresh(db_requirement)

    # 构造响应数据
    response_data = db_requirement.__dict__.copy()

    # 获取开发人员信息
    developers = []
    developer_records = db.query(User).join(
        RequirementAssignment,
        User.id == RequirementAssignment.user_id
    ).filter(
        RequirementAssignment.requirement_id == db_requirement.id
    ).all()

    for dev in developer_records:
        developers.append({
            "id": dev.id,
            "name": dev.name,
            "username": dev.username
        })

    # 获取分支信息
    main_branch = ""
    other_branches = []
    branch_records = db.query(RequirementBranch).filter(
        RequirementBranch.requirement_id == db_requirement.id
    ).all()

    for branch in branch_records:
        if branch.is_main:
            main_branch = branch.branch_name
        else:
            other_branches.append(branch.branch_name)

    # 添加到响应
    response_data["developers"] = developers
    response_data["main_branch"] = main_branch
    response_data["other_branches"] = other_branches

    # 发送企业微信通知（只有非草稿状态才发送通知）
    if initial_status != RequirementStatus.DRAFT:
        try:
            NotificationService.send_requirement_created_notification(
                db=db,
                requirement=db_requirement,
                submitter=current_user,
                project=project,
                developer_ids=requirement.developer_ids
            )
        except Exception as e:
            logger.error(f"发送企业微信通知失败: {str(e)}")
            # 通知失败不影响API响应

    return response_data

# 发布草稿需求
@router.post("/requirements/{requirement_id}/publish", response_model=RequirementResponse)
def publish_requirement(
    requirement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    发布草稿需求
    """
    db_requirement = db.query(RequirementModel).filter(RequirementModel.id == requirement_id).first()

    if not db_requirement:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="需求不存在")

    if db_requirement.status != RequirementStatus.DRAFT:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="只能发布草稿状态的需求")

    # 检查权限：管理员可以发布任何草稿，普通用户只能发布自己创建的草稿
    if not current_user.is_admin and db_requirement.submitter_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="只能发布自己创建的草稿需求")

    # 验证必要字段是否完整
    if not db_requirement.content or not db_requirement.type or not db_requirement.start_date or not db_requirement.end_date:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="需求信息不完整，无法发布")

    # 获取项目信息
    project = db.query(Project).filter(Project.id == db_requirement.project_id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    # 创建GitLab分支
    try:
        logger.info(f"[需求发布] 调用AI服务翻译需求标题")
        branch_name = ai_service.translate_title_to_branch_name(db_requirement.title)
        logger.info(f"[需求发布] AI成功生成分支名称: '{branch_name}'")

        # 根据需求类型确定分支前缀
        if db_requirement.type == RequirementType.BUG_FIX:
            prefix = "bugfix"
        elif db_requirement.type == RequirementType.HOT_FIX_BUG:
            prefix = "hotfix"
        else:
            prefix = "feature"

        gitlab_branch_name = f"{prefix}/{branch_name}"

        # 获取主目标分支（从数据库中获取）
        main_branch_record = db.query(RequirementBranch).filter(
            RequirementBranch.requirement_id == requirement_id,
            RequirementBranch.is_main == True
        ).first()

        if main_branch_record:
            source_branch = main_branch_record.branch_name
        else:
            source_branch = "main"  # 默认分支

        logger.info(f"[需求发布] 创建GitLab分支: {gitlab_branch_name}, 源分支: {source_branch}")
        gitlab_api.create_branch(project.id, gitlab_branch_name, source_branch)
        logger.info(f"[需求发布] GitLab分支创建成功")

    except Exception as e:
        logger.error(f"[GitLab] 创建分支失败: {str(e)}")
        gitlab_branch_name = None

    # 更新需求状态
    db_requirement.status = RequirementStatus.PENDING
    db_requirement.git_branch = gitlab_branch_name
    db_requirement.update_time = get_utc_now()

    # 记录状态变更
    history = RequirementHistory(
        requirement_id=requirement_id,
        current_status_code=RequirementStatus.PENDING.name,
        action_code=RequirementAction.PUBLISH.name,
        user_id=current_user.id,
        action_time=get_utc_now()
    )
    db.add(history)

    db.commit()
    db.refresh(db_requirement)

    # 构造响应数据
    result = db_requirement.__dict__.copy()

    # 获取开发人员
    developers = []
    developer_records = db.query(User).join(
        RequirementAssignment,
        User.id == RequirementAssignment.user_id
    ).filter(
        RequirementAssignment.requirement_id == db_requirement.id
    ).all()

    for dev in developer_records:
        developers.append({
            "id": dev.id,
            "name": dev.name,
            "username": dev.username
        })

    # 获取分支
    main_branch = ""
    other_branches = []
    branch_records = db.query(RequirementBranch).filter(
        RequirementBranch.requirement_id == db_requirement.id
    ).all()

    for branch in branch_records:
        if branch.is_main:
            main_branch = branch.branch_name
        else:
            other_branches.append(branch.branch_name)

    # 添加到响应
    result["developers"] = developers
    result["main_branch"] = main_branch
    result["other_branches"] = other_branches

    # 发送企业微信通知
    try:
        developer_ids = [dev["id"] for dev in developers]
        NotificationService.send_requirement_created_notification(
            db=db,
            requirement=db_requirement,
            submitter=current_user,
            project=project,
            developer_ids=developer_ids
        )
    except Exception as e:
        logger.error(f"发送企业微信通知失败: {str(e)}")

    return result

@router.get("/requirements", response_model=List[RequirementResponse])
def get_requirements(
    project_id: Optional[int] = None,
    type: Optional[str] = None,
    priority: Optional[str] = None,
    status: Optional[str] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取需求列表，支持按项目ID、类型、优先级、状态筛选
    """
    query = db.query(RequirementModel)

    # 筛选条件
    if project_id:
        query = query.filter(RequirementModel.project_id == project_id)
    if type:
        query = query.filter(RequirementModel.type == type)
    if priority:
        query = query.filter(RequirementModel.priority == priority)
    if status:
        query = query.filter(RequirementModel.status == status)

    # 非管理员只能看到自己参与的需求（作为开发人员、测试人员或创建者）
    if not current_user.is_admin:
        # 使用子查询检查用户是否为开发人员
        developer_subquery = db.query(RequirementAssignment.requirement_id).filter(
            RequirementAssignment.user_id == current_user.id
        ).subquery()

        # 使用子查询检查用户是否为测试人员
        tester_subquery = db.query(RequirementModel.id).join(
            project_testers_table, RequirementModel.project_id == project_testers_table.c.project_id
        ).filter(
            project_testers_table.c.user_id == current_user.id
        ).subquery()

        # 过滤条件：用户是开发人员、测试人员或创建者，且需求未完成
        query = query.filter(
            ((RequirementModel.id.in_(developer_subquery)) |
             (RequirementModel.id.in_(tester_subquery)) |
             (RequirementModel.submitter_id == current_user.id)) &
            (RequirementModel.status != 'COMPLETED')
        )

    # 默认按优先级、计划完成时间排序
    query = query.order_by(
        RequirementModel.priority,
        RequirementModel.end_date
    )

    # 分页
    requirements = query.offset(skip).limit(limit).all()

    # 构造响应列表
    result = []
    for req in requirements:
        req_dict = req.__dict__.copy()

        # 获取开发人员
        developers = []
        developer_records = db.query(User).join(
            RequirementAssignment,
            User.id == RequirementAssignment.user_id
        ).filter(
            RequirementAssignment.requirement_id == req.id
        ).all()

        for dev in developer_records:
            developers.append({
                "id": dev.id,
                "name": dev.name,
                "username": dev.username
            })

        # 获取分支
        main_branch = ""
        other_branches = []
        branch_records = db.query(RequirementBranch).filter(
            RequirementBranch.requirement_id == req.id
        ).all()

        for branch in branch_records:
            if branch.is_main:
                main_branch = branch.branch_name
            else:
                other_branches.append(branch.branch_name)

        # 添加到响应
        req_dict["developers"] = developers
        req_dict["main_branch"] = main_branch
        req_dict["other_branches"] = other_branches
        # 保留git_branch字段，不需要额外添加，因为它已经在req_dict中

        result.append(req_dict)

    return result

@router.get("/requirements/{requirement_id}", response_model=RequirementResponse)
def get_requirement(
    requirement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取单个需求详情
    """
    requirement = db.query(RequirementModel).filter(RequirementModel.id == requirement_id).first()

    if not requirement:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="需求不存在")

    # 非管理员检查权限
    if not current_user.is_admin:
        # 检查用户是否为开发人员
        is_assigned = db.query(RequirementAssignment).filter(
            RequirementAssignment.requirement_id == requirement_id,
            RequirementAssignment.user_id == current_user.id
        ).first()

        # 检查用户是否为测试人员
        is_tester = db.query(project_testers_table).filter(
            project_testers_table.c.project_id == requirement.project_id,
            project_testers_table.c.user_id == current_user.id
        ).first()

        # 检查用户是否为创建者
        is_creator = requirement.submitter_id == current_user.id

        if not (is_assigned or is_tester or is_creator):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限查看此需求"
            )

    # 构造响应数据
    result = requirement.__dict__.copy()

    # 获取开发人员
    developers = []
    developer_records = db.query(User).join(
        RequirementAssignment,
        User.id == RequirementAssignment.user_id
    ).filter(
        RequirementAssignment.requirement_id == requirement.id
    ).all()

    for dev in developer_records:
        developers.append({
            "id": dev.id,
            "name": dev.name,
            "username": dev.username
        })

    # 获取分支
    main_branch = ""
    other_branches = []
    branch_records = db.query(RequirementBranch).filter(
        RequirementBranch.requirement_id == requirement.id
    ).all()

    for branch in branch_records:
        if branch.is_main:
            main_branch = branch.branch_name
        else:
            other_branches.append(branch.branch_name)

    # 添加到响应
    result["developers"] = developers
    result["main_branch"] = main_branch
    result["other_branches"] = other_branches
    # git_branch字段已经在result中，不需要额外添加

    return result

@router.put("/requirements/{requirement_id}", response_model=RequirementResponse)
def update_requirement(
    requirement_id: int,
    requirement_update: RequirementUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新需求
    """
    db_requirement = db.query(RequirementModel).filter(RequirementModel.id == requirement_id).first()

    if not db_requirement:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="需求不存在")

    # 非管理员检查权限
    if not current_user.is_admin:
        # 检查用户是否为开发人员
        is_assigned = db.query(RequirementAssignment).filter(
            RequirementAssignment.requirement_id == requirement_id,
            RequirementAssignment.user_id == current_user.id
        ).first()

        # 检查用户是否为测试人员
        is_tester = db.query(project_testers_table).filter(
            project_testers_table.c.project_id == db_requirement.project_id,
            project_testers_table.c.user_id == current_user.id
        ).first()

        # 检查用户是否为创建者
        is_creator = db_requirement.submitter_id == current_user.id

        if not (is_assigned or is_tester or is_creator):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限更新此需求"
            )

        # 普通用户只能更新状态
        if requirement_update.status:
            previous_status = db_requirement.status
            db_requirement.status = requirement_update.status
            db_requirement.update_time = get_utc_now()

            # 记录状态变更
            history = RequirementHistory(
                requirement_id=requirement_id,
                current_status_code=requirement_update.status.name,
                action_code=RequirementAction.SUBMIT.name,  # 使用提交需求作为默认动作
                user_id=current_user.id
            )
            db.add(history)

            db.commit()
            db.refresh(db_requirement)
        else:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="普通用户只能更新需求状态"
            )
    else:
        # 管理员可以更新所有字段
        update_data = requirement_update.model_dump(exclude_unset=True, exclude={"developer_ids"})

        # 如果有状态变更，记录历史
        if "status" in update_data and update_data["status"] != db_requirement.status:
            history = RequirementHistory(
                requirement_id=requirement_id,
                current_status_code=update_data["status"].name,
                action_code=RequirementAction.SUBMIT.name,  # 使用提交需求作为默认动作
                user_id=current_user.id
            )
            db.add(history)

        # 更新基本信息
        for key, value in update_data.items():
            setattr(db_requirement, key, value)

        db_requirement.update_time = get_utc_now()

        # 更新开发人员
        if requirement_update.developer_ids is not None:
            db.query(RequirementAssignment).filter(
                RequirementAssignment.requirement_id == requirement_id
            ).delete()

            for dev_id in requirement_update.developer_ids:
                assignment = RequirementAssignment(
                    requirement_id=requirement_id,
                    user_id=dev_id
                )
                db.add(assignment)

        # 分支信息在修改需求时不可编辑，因此移除相关代码

        db.commit()
        db.refresh(db_requirement)

    # 构造响应数据
    result = db_requirement.__dict__.copy()

    # 获取开发人员
    developers = []
    developer_records = db.query(User).join(
        RequirementAssignment,
        User.id == RequirementAssignment.user_id
    ).filter(
        RequirementAssignment.requirement_id == db_requirement.id
    ).all()

    for dev in developer_records:
        developers.append({
            "id": dev.id,
            "name": dev.name,
            "username": dev.username
        })

    # 获取分支
    main_branch = ""
    other_branches = []
    branch_records = db.query(RequirementBranch).filter(
        RequirementBranch.requirement_id == db_requirement.id
    ).all()

    for branch in branch_records:
        if branch.is_main:
            main_branch = branch.branch_name
        else:
            other_branches.append(branch.branch_name)

    # 添加到响应
    result["developers"] = developers
    result["main_branch"] = main_branch
    result["other_branches"] = other_branches
    # git_branch字段已经在result中，不需要额外添加

    # 发送企业微信通知
    try:
        # 获取项目信息
        project = db.query(Project).filter(Project.id == db_requirement.project_id).first()

        # 获取更新的字段列表
        updated_fields = []
        if isinstance(requirement_update, dict):
            updated_fields = list(requirement_update.keys())
        else:
            # 如果是Pydantic模型，获取已设置的字段
            updated_fields = list(requirement_update.model_dump(exclude_unset=True).keys())

        # 获取开发人员ID列表
        developer_ids = [dev["id"] for dev in developers]

        NotificationService.send_requirement_updated_notification(
            db=db,
            requirement=db_requirement,
            updater=current_user,
            project=project,
            developer_ids=developer_ids,
            updated_fields=updated_fields
        )
    except Exception as e:
        logger.error(f"发送企业微信通知失败: {str(e)}")
        # 通知失败不影响API响应

    return result

# 开发人员认领需求
@router.post("/requirements/{requirement_id}/claim", response_model=RequirementResponse)
def claim_requirement(
    requirement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    开发人员认领需求
    """
    db_requirement = db.query(RequirementModel).filter(RequirementModel.id == requirement_id).first()

    if not db_requirement:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="需求不存在")

    # 检查需求状态
    if db_requirement.status != RequirementStatus.PENDING:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="只能认领待处理状态的需求")

    # 检查用户是否为开发人员
    is_developer = db.query(RequirementAssignment).filter(
        RequirementAssignment.requirement_id == requirement_id,
        RequirementAssignment.user_id == current_user.id
    ).first()

    if not is_developer and not current_user.is_admin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="只有开发人员可以认领需求")

    # 更新需求状态
    previous_status = db_requirement.status
    db_requirement.status = RequirementStatus.DEVELOPING
    db_requirement.update_time = get_utc_now()

    # 记录状态变更
    history = RequirementHistory(
        requirement_id=requirement_id,
        current_status_code=RequirementStatus.DEVELOPING.name,
        action_code=RequirementAction.CLAIM.name,
        user_id=current_user.id
    )
    db.add(history)

    db.commit()
    db.refresh(db_requirement)

    # 构造响应数据
    result = db_requirement.__dict__.copy()

    # 获取开发人员
    developers = []
    developer_records = db.query(User).join(
        RequirementAssignment,
        User.id == RequirementAssignment.user_id
    ).filter(
        RequirementAssignment.requirement_id == db_requirement.id
    ).all()

    for dev in developer_records:
        developers.append({
            "id": dev.id,
            "name": dev.name,
            "username": dev.username
        })

    # 获取分支
    main_branch = ""
    other_branches = []
    branch_records = db.query(RequirementBranch).filter(
        RequirementBranch.requirement_id == db_requirement.id
    ).all()

    for branch in branch_records:
        if branch.is_main:
            main_branch = branch.branch_name
        else:
            other_branches.append(branch.branch_name)

    # 添加到响应
    result["developers"] = developers
    result["main_branch"] = main_branch
    result["other_branches"] = other_branches

    # 发送企业微信通知
    try:
        project = db.query(Project).filter(Project.id == db_requirement.project_id).first()
        if project:
            NotificationService.send_requirement_status_change_notification(
                db=db,
                requirement=db_requirement,
                operator=current_user,
                project=project,
                action="认领需求",
                new_status="开发中"
            )
    except Exception as e:
        logger.error(f"发送企业微信通知失败: {str(e)}")

    return result

# 开发人员提交测试
@router.post("/requirements/{requirement_id}/submit-to-test", response_model=RequirementResponse)
def submit_to_test(
    requirement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    开发人员提交需求到测试阶段
    """
    db_requirement = db.query(RequirementModel).filter(RequirementModel.id == requirement_id).first()

    if not db_requirement:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="需求不存在")

    # 检查需求状态
    if db_requirement.status != RequirementStatus.DEVELOPING:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="只能提交开发中状态的需求")

    # 检查用户是否为开发人员
    is_developer = db.query(RequirementAssignment).filter(
        RequirementAssignment.requirement_id == requirement_id,
        RequirementAssignment.user_id == current_user.id
    ).first()

    if not is_developer and not current_user.is_admin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="只有开发人员可以提交需求到测试阶段")

    # 更新需求状态
    previous_status = db_requirement.status
    db_requirement.status = RequirementStatus.TESTING
    db_requirement.update_time = get_utc_now()

    # 记录状态变更
    history = RequirementHistory(
        requirement_id=requirement_id,
        current_status_code=RequirementStatus.TESTING.name,
        action_code=RequirementAction.SUBMIT_TO_TEST.name,
        user_id=current_user.id
    )
    db.add(history)

    db.commit()
    db.refresh(db_requirement)

    # 构造响应数据
    result = db_requirement.__dict__.copy()

    # 获取开发人员
    developers = []
    developer_records = db.query(User).join(
        RequirementAssignment,
        User.id == RequirementAssignment.user_id
    ).filter(
        RequirementAssignment.requirement_id == db_requirement.id
    ).all()

    for dev in developer_records:
        developers.append({
            "id": dev.id,
            "name": dev.name,
            "username": dev.username
        })

    # 获取分支
    main_branch = ""
    other_branches = []
    branch_records = db.query(RequirementBranch).filter(
        RequirementBranch.requirement_id == db_requirement.id
    ).all()

    for branch in branch_records:
        if branch.is_main:
            main_branch = branch.branch_name
        else:
            other_branches.append(branch.branch_name)

    # 添加到响应
    result["developers"] = developers
    result["main_branch"] = main_branch
    result["other_branches"] = other_branches

    # 发送企业微信通知
    try:
        project = db.query(Project).filter(Project.id == db_requirement.project_id).first()
        if project:
            NotificationService.send_requirement_status_change_notification(
                db=db,
                requirement=db_requirement,
                operator=current_user,
                project=project,
                action="提交测试",
                new_status="测试中"
            )
    except Exception as e:
        logger.error(f"发送企业微信通知失败: {str(e)}")

    return result

# 开发人员撤回测试
@router.post("/requirements/{requirement_id}/withdraw-from-test", response_model=RequirementResponse)
def withdraw_from_test(
    requirement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    开发人员撤回测试中的需求
    """
    db_requirement = db.query(RequirementModel).filter(RequirementModel.id == requirement_id).first()

    if not db_requirement:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="需求不存在")

    # 检查需求状态
    if db_requirement.status != RequirementStatus.TESTING:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="只能撤回测试中状态的需求")

    # 检查用户是否为开发人员
    is_developer = db.query(RequirementAssignment).filter(
        RequirementAssignment.requirement_id == requirement_id,
        RequirementAssignment.user_id == current_user.id
    ).first()

    if not is_developer and not current_user.is_admin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="只有开发人员可以撤回测试")

    # 更新需求状态
    previous_status = db_requirement.status
    db_requirement.status = RequirementStatus.DEVELOPING
    db_requirement.update_time = get_utc_now()

    # 记录状态变更
    history = RequirementHistory(
        requirement_id=requirement_id,
        current_status_code=RequirementStatus.DEVELOPING.name,
        action_code=RequirementAction.WITHDRAW_TEST.name,
        user_id=current_user.id
    )
    db.add(history)

    db.commit()
    db.refresh(db_requirement)

    # 构造响应数据
    result = db_requirement.__dict__.copy()

    # 获取开发人员
    developers = []
    developer_records = db.query(User).join(
        RequirementAssignment,
        User.id == RequirementAssignment.user_id
    ).filter(
        RequirementAssignment.requirement_id == db_requirement.id
    ).all()

    for dev in developer_records:
        developers.append({
            "id": dev.id,
            "name": dev.name,
            "username": dev.username
        })

    # 获取分支
    main_branch = ""
    other_branches = []
    branch_records = db.query(RequirementBranch).filter(
        RequirementBranch.requirement_id == db_requirement.id
    ).all()

    for branch in branch_records:
        if branch.is_main:
            main_branch = branch.branch_name
        else:
            other_branches.append(branch.branch_name)

    # 添加到响应
    result["developers"] = developers
    result["main_branch"] = main_branch
    result["other_branches"] = other_branches

    # 发送企业微信通知
    try:
        project = db.query(Project).filter(Project.id == db_requirement.project_id).first()
        if project:
            NotificationService.send_requirement_status_change_notification(
                db=db,
                requirement=db_requirement,
                operator=current_user,
                project=project,
                action="撤回测试",
                new_status="开发中"
            )
    except Exception as e:
        logger.error(f"发送企业微信通知失败: {str(e)}")

    return result

# 开发人员撤回验证
@router.post("/requirements/{requirement_id}/withdraw-from-validation", response_model=RequirementResponse)
def withdraw_from_validation(
    requirement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    开发人员撤回验证中的需求
    """
    db_requirement = db.query(RequirementModel).filter(RequirementModel.id == requirement_id).first()

    if not db_requirement:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="需求不存在")

    # 检查需求状态
    if db_requirement.status != "验证中":  # 使用字符串而不是枚举，因为枚举中可能没有这个值
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="只能撤回验证中状态的需求")

    # 检查用户是否为开发人员
    is_developer = db.query(RequirementAssignment).filter(
        RequirementAssignment.requirement_id == requirement_id,
        RequirementAssignment.user_id == current_user.id
    ).first()

    if not is_developer and not current_user.is_admin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="只有开发人员可以撤回验证")

    # 更新需求状态
    previous_status = db_requirement.status
    db_requirement.status = RequirementStatus.TESTING
    db_requirement.update_time = get_utc_now()

    # 记录状态变更
    history = RequirementHistory(
        requirement_id=requirement_id,
        current_status_code=RequirementStatus.TESTING.name,
        action_code=RequirementAction.WITHDRAW_VALIDATION.name,
        user_id=current_user.id
    )
    db.add(history)

    db.commit()
    db.refresh(db_requirement)

    # 构造响应数据
    result = db_requirement.__dict__.copy()

    # 获取开发人员
    developers = []
    developer_records = db.query(User).join(
        RequirementAssignment,
        User.id == RequirementAssignment.user_id
    ).filter(
        RequirementAssignment.requirement_id == db_requirement.id
    ).all()

    for dev in developer_records:
        developers.append({
            "id": dev.id,
            "name": dev.name,
            "username": dev.username
        })

    # 获取分支
    main_branch = ""
    other_branches = []
    branch_records = db.query(RequirementBranch).filter(
        RequirementBranch.requirement_id == db_requirement.id
    ).all()

    for branch in branch_records:
        if branch.is_main:
            main_branch = branch.branch_name
        else:
            other_branches.append(branch.branch_name)

    # 添加到响应
    result["developers"] = developers
    result["main_branch"] = main_branch
    result["other_branches"] = other_branches

    # 发送企业微信通知
    try:
        project = db.query(Project).filter(Project.id == db_requirement.project_id).first()
        if project:
            NotificationService.send_requirement_status_change_notification(
                db=db,
                requirement=db_requirement,
                operator=current_user,
                project=project,
                action="撤回验证",
                new_status="测试中"
            )
    except Exception as e:
        logger.error(f"发送企业微信通知失败: {str(e)}")

    return result

# 测试人员通过测试
@router.post("/requirements/{requirement_id}/approve-test", response_model=RequirementResponse)
def approve_test(
    requirement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    测试人员通过测试
    """
    db_requirement = db.query(RequirementModel).filter(RequirementModel.id == requirement_id).first()

    if not db_requirement:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="需求不存在")

    # 检查需求状态
    if db_requirement.status != RequirementStatus.TESTING:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="只能通过测试中状态的需求")

    # 检查用户是否为测试人员
    project = db.query(Project).filter(Project.id == db_requirement.project_id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    is_tester = False
    for tester in project.testers:
        if tester.id == current_user.id:
            is_tester = True
            break

    if not is_tester and not current_user.is_admin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="只有测试人员可以通过测试")

    # 更新需求状态
    previous_status = db_requirement.status
    db_requirement.status = RequirementStatus.VALIDATING
    db_requirement.update_time = get_utc_now()

    # 记录状态变更
    history = RequirementHistory(
        requirement_id=requirement_id,
        current_status_code=RequirementStatus.VALIDATING.name,
        action_code=RequirementAction.APPROVE_TEST.name,
        user_id=current_user.id
    )
    db.add(history)

    db.commit()
    db.refresh(db_requirement)

    # 构造响应数据
    result = db_requirement.__dict__.copy()

    # 获取开发人员
    developers = []
    developer_records = db.query(User).join(
        RequirementAssignment,
        User.id == RequirementAssignment.user_id
    ).filter(
        RequirementAssignment.requirement_id == db_requirement.id
    ).all()

    for dev in developer_records:
        developers.append({
            "id": dev.id,
            "name": dev.name,
            "username": dev.username
        })

    # 获取分支
    main_branch = ""
    other_branches = []
    branch_records = db.query(RequirementBranch).filter(
        RequirementBranch.requirement_id == db_requirement.id
    ).all()

    for branch in branch_records:
        if branch.is_main:
            main_branch = branch.branch_name
        else:
            other_branches.append(branch.branch_name)

    # 添加到响应
    result["developers"] = developers
    result["main_branch"] = main_branch
    result["other_branches"] = other_branches

    # 发送企业微信通知
    try:
        NotificationService.send_requirement_status_change_notification(
            db=db,
            requirement=db_requirement,
            operator=current_user,
            project=project,
            action="通过测试",
            new_status="验证中"
        )
    except Exception as e:
        logger.error(f"发送企业微信通知失败: {str(e)}")

    return result

# 测试人员驳回测试
@router.post("/requirements/{requirement_id}/reject-test", response_model=RequirementResponse)
def reject_test(
    requirement_id: int,
    reason: dict = Body(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    测试人员驳回测试
    """
    db_requirement = db.query(RequirementModel).filter(RequirementModel.id == requirement_id).first()

    if not db_requirement:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="需求不存在")

    # 检查需求状态
    if db_requirement.status != RequirementStatus.TESTING:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="只能驳回测试中状态的需求")

    # 检查用户是否为测试人员
    project = db.query(Project).filter(Project.id == db_requirement.project_id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    is_tester = False
    for tester in project.testers:
        if tester.id == current_user.id:
            is_tester = True
            break

    if not is_tester and not current_user.is_admin:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="只有测试人员可以驳回测试")

    # 更新需求状态
    _ = db_requirement.status  # 仅用于记录，不使用
    db_requirement.status = RequirementStatus.DEVELOPING
    db_requirement.update_time = get_utc_now()

    # 记录状态变更
    history = RequirementHistory(
        requirement_id=requirement_id,
        current_status_code=RequirementStatus.DEVELOPING.name,
        action_code=RequirementAction.REJECT_TEST.name,
        user_id=current_user.id,
        remark=reason.get("reason") if reason else None
    )
    db.add(history)

    db.commit()
    db.refresh(db_requirement)

    # 构造响应数据
    result = db_requirement.__dict__.copy()

    # 获取开发人员
    developers = []
    developer_records = db.query(User).join(
        RequirementAssignment,
        User.id == RequirementAssignment.user_id
    ).filter(
        RequirementAssignment.requirement_id == db_requirement.id
    ).all()

    for dev in developer_records:
        developers.append({
            "id": dev.id,
            "name": dev.name,
            "username": dev.username
        })

    # 获取分支
    main_branch = ""
    other_branches = []
    branch_records = db.query(RequirementBranch).filter(
        RequirementBranch.requirement_id == db_requirement.id
    ).all()

    for branch in branch_records:
        if branch.is_main:
            main_branch = branch.branch_name
        else:
            other_branches.append(branch.branch_name)

    # 添加到响应
    result["developers"] = developers
    result["main_branch"] = main_branch
    result["other_branches"] = other_branches

    # 发送企业微信通知
    try:
        project = db.query(Project).filter(Project.id == db_requirement.project_id).first()
        if project:
            NotificationService.send_requirement_status_change_notification(
                db=db,
                requirement=db_requirement,
                operator=current_user,
                project=project,
                action="驳回测试",
                new_status="开发中",
                reason=reason.get("reason") if reason else None
            )
    except Exception as e:
        logger.error(f"发送企业微信通知失败: {str(e)}")

    return result

@router.delete("/requirements/{requirement_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_requirement(
    requirement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除需求
    """
    # 检查用户权限，只有管理员可以删除需求
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以删除需求"
        )

    # 查询需求是否存在
    db_requirement = db.query(RequirementModel).filter(RequirementModel.id == requirement_id).first()
    if not db_requirement:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="需求不存在")

    # 查询项目信息
    project = db.query(Project).filter(Project.id == db_requirement.project_id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    # 获取开发人员和测试人员信息，用于发送通知
    developers = []
    developer_records = db.query(User).join(
        RequirementAssignment,
        User.id == RequirementAssignment.user_id
    ).filter(
        RequirementAssignment.requirement_id == requirement_id
    ).all()

    for dev in developer_records:
        developers.append({
            "id": dev.id,
            "name": dev.name,
            "username": dev.username
        })

    # 获取测试人员信息
    testers = []
    tester_records = db.query(User).join(
        project_testers_table,
        User.id == project_testers_table.c.user_id
    ).filter(
        project_testers_table.c.project_id == db_requirement.project_id
    ).all()

    for tester in tester_records:
        testers.append({
            "id": tester.id,
            "name": tester.name,
            "username": tester.username
        })

    # 发送企业微信通知（根据需求状态决定通知对象）
    try:
        if db_requirement.status != RequirementStatus.DRAFT:  # 草稿状态不通知
            developer_ids = [dev["id"] for dev in developers]
            tester_ids = [tester["id"] for tester in testers]

            # 根据状态决定通知对象
            notify_user_ids = []
            if db_requirement.status == RequirementStatus.PENDING:  # 待认领：通知项目开发人员
                notify_user_ids = developer_ids
            elif db_requirement.status == RequirementStatus.DEVELOPING:  # 开发中：通知项目开发人员 + 项目测试人员
                notify_user_ids = developer_ids + tester_ids
            elif db_requirement.status in [RequirementStatus.TESTING, RequirementStatus.VALIDATING]:  # 测试中、验证中：通知项目开发人员 + 项目测试人员
                notify_user_ids = developer_ids + tester_ids

            if notify_user_ids:
                NotificationService.send_requirement_deleted_notification(
                    db=db,
                    requirement=db_requirement,
                    deleter=current_user,
                    project=project,
                    notify_user_ids=notify_user_ids
                )
    except Exception as e:
        logger.error(f"发送企业微信通知失败: {str(e)}")
        # 通知失败不影响删除操作

    # 删除GitLab分支
    if db_requirement.git_branch:
        try:
            logger.info(f"[GitLab] 准备删除分支: {db_requirement.git_branch}, 项目ID: {project.id}")
            success = gitlab_api.delete_branch(
                project_id=project.id,
                branch_name=db_requirement.git_branch
            )
            if success:
                logger.info(f"[GitLab] 分支删除成功: {db_requirement.git_branch}")
            else:
                logger.warning(f"[GitLab] 分支删除失败: {db_requirement.git_branch}")
        except Exception as e:
            logger.error(f"[GitLab] 删除分支时发生错误: {str(e)}")
            # 分支删除失败不阻止需求删除，只记录错误日志

    # 删除关联的开发人员分配记录
    db.query(RequirementAssignment).filter(
        RequirementAssignment.requirement_id == requirement_id
    ).delete()

    # 删除关联的分支记录
    db.query(RequirementBranch).filter(
        RequirementBranch.requirement_id == requirement_id
    ).delete()

    # 删除关联的历史记录
    db.query(RequirementHistory).filter(
        RequirementHistory.requirement_id == requirement_id
    ).delete()

    # 删除关联的PR记录
    db.query(RequirementPullRequest).filter(
        RequirementPullRequest.requirement_id == requirement_id
    ).delete()
    logger.info(f"[需求删除] 删除需求 {requirement_id} 的相关PR记录")

    # 删除需求本身
    db.delete(db_requirement)
    db.commit()

    return
@router.get("/requirement-detail/{requirement_id}", response_model=dict)
def get_requirement_detail(
    requirement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取需求详情，包含更多信息
    """
    # 查询需求基本信息
    requirement = db.query(RequirementModel).filter(RequirementModel.id == requirement_id).first()

    if not requirement:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="需求不存在")

    # 查询项目信息
    project = db.query(Project).filter(Project.id == requirement.project_id).first()
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    # 查询提交人信息
    submitter = db.query(User).filter(User.id == requirement.submitter_id).first()

    # 查询开发人员信息
    developers = []
    developer_records = db.query(User).join(
        RequirementAssignment,
        User.id == RequirementAssignment.user_id
    ).filter(
        RequirementAssignment.requirement_id == requirement.id
    ).all()

    for dev in developer_records:
        developers.append({
            "id": dev.id,
            "name": dev.name,
            "username": dev.username
        })

    # 查询分支信息
    main_branch = ""
    other_branches = []
    branch_records = db.query(RequirementBranch).filter(
        RequirementBranch.requirement_id == requirement.id
    ).all()

    for branch in branch_records:
        if branch.is_main:
            main_branch = branch.branch_name
        else:
            other_branches.append(branch.branch_name)

    # 查询历史记录
    histories = []
    history_records = db.query(RequirementHistory).filter(
        RequirementHistory.requirement_id == requirement.id
    ).order_by(RequirementHistory.action_time.desc()).all()

    for history in history_records:
        history_user = db.query(User).filter(User.id == history.user_id).first()

        # 获取状态和动作的中文显示
        current_status_display = None
        for status in RequirementStatus:
            if status.name == history.current_status_code:
                current_status_display = status.value
                break

        action_display = None
        for action in RequirementAction:
            if action.name == history.action_code:
                action_display = action.value
                break

        histories.append({
            "id": history.id,
            "current_status": current_status_display,
            "action": action_display,
            "action_time": history.action_time,
            "remark": history.remark,
            "user": {
                "id": history_user.id if history_user else None,
                "name": history_user.name if history_user else "未知用户",
                "username": history_user.username if history_user else None
            }
        })

    # 查询PR信息
    pull_requests = []
    pr_records = db.query(RequirementPullRequest).filter(
        RequirementPullRequest.requirement_id == requirement.id
    ).all()

    for pr in pr_records:
        pull_requests.append({
            "id": pr.id,
            "source_branch": pr.source_branch,
            "target_branch": pr.target_branch,
            "pr_iid": pr.pr_iid,
            "pr_url": pr.pr_url,
            "status": pr.status.value,  # 使用枚举的值（中文）
            "create_time": pr.create_time,
            "update_time": pr.update_time
        })

    # 构造响应数据
    result = {
        "id": requirement.id,
        "project_id": requirement.project_id,
        "project_name": project.name if project else None,
        "requirement_code": requirement.requirement_code,
        "title": requirement.title,
        "content": requirement.content,
        "type": requirement.type,
        "priority": requirement.priority,
        "status": requirement.status,
        "start_date": requirement.start_date,
        "end_date": requirement.end_date,
        "submitter": {
            "id": submitter.id if submitter else None,
            "name": submitter.name if submitter else "未知用户",
            "username": submitter.username if submitter else None
        },
        "submit_time": requirement.submit_time,
        "update_time": requirement.update_time,
        "developers": developers,
        "main_branch": main_branch,
        "other_branches": other_branches,
        "git_branch": requirement.git_branch,  # 添加Git分支名称
        "histories": histories,
        "pull_requests": pull_requests  # 添加PR信息
    }

    return result

# 管理员通过验证
@router.post("/requirements/{requirement_id}/approve-validation", status_code=status.HTTP_200_OK)
def approve_validation(
    requirement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    管理员通过验证，将需求状态更新为已完成，并为所有目标分支创建合并请求
    """
    # 检查用户权限
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以执行此操作"
        )

    # 查询需求
    db_requirement = db.query(RequirementModel).filter(RequirementModel.id == requirement_id).first()
    if not db_requirement:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="需求不存在")

    # 检查需求状态
    if db_requirement.status != RequirementStatus.VALIDATING:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"只有验证中状态的需求可以通过验证，当前状态: {db_requirement.status.value}"
        )

    # 获取当前状态
    _ = db_requirement.status  # 仅用于记录，不使用

    # 更新需求状态
    db_requirement.status = RequirementStatus.COMPLETED
    db_requirement.update_time = get_utc_now()

    # 记录状态变更
    history = RequirementHistory(
        requirement_id=requirement_id,
        current_status_code=RequirementStatus.COMPLETED.name,
        action_code=RequirementAction.APPROVE_TEST.name,  # 使用通过测试作为动作
        user_id=current_user.id
    )

    db.add(history)
    db.commit()

    # 获取项目信息
    project = db.query(Project).filter(Project.id == db_requirement.project_id).first()
    if not project:
        # 如果找不到项目，仍然完成验证，但记录错误
        logger.error(f"[GitLab] 创建合并请求失败: 找不到项目ID {db_requirement.project_id}")
        return {"message": "需求验证已通过，状态已更新为已完成，但无法创建合并请求：找不到项目"}

    # 获取需求的Git分支（源分支）
    source_branch = db_requirement.git_branch
    if not source_branch:
        # 如果没有源分支，仍然完成验证，但记录错误
        logger.error(f"[GitLab] 创建合并请求失败: 需求 {requirement_id} 没有关联的Git分支")
        return {"message": "需求验证已通过，状态已更新为已完成，但无法创建合并请求：需求没有关联的Git分支"}

    # 获取所有目标分支（主分支和其他分支）
    branch_records = db.query(RequirementBranch).filter(
        RequirementBranch.requirement_id == requirement_id
    ).all()

    if not branch_records:
        # 如果没有目标分支，仍然完成验证，但记录错误
        logger.error(f"[GitLab] 创建合并请求失败: 需求 {requirement_id} 没有关联的目标分支")
        return {"message": "需求验证已通过，状态已更新为已完成，但无法创建合并请求：需求没有关联的目标分支"}

    # 为每个目标分支创建合并请求
    merge_requests_results = []
    for branch_record in branch_records:
        target_branch = branch_record.branch_name
        try:
            # 创建合并请求标题和描述
            title = f"Merge {source_branch} to {target_branch}"
            description = f"需求: {db_requirement.title}"

            # 调用GitLab API创建合并请求
            logger.info(f"[GitLab] 准备创建合并请求: 源分支={source_branch}, 目标分支={target_branch}")
            result = gitlab_api.create_merge_request(
                project_id=project.id,
                source_branch=source_branch,
                target_branch=target_branch,
                title=title,
                description=description
            )

            logger.info(f"[GitLab] 合并请求创建成功: {result.get('web_url', '未知URL')}")

            # 保存PR信息到数据库
            pr_record = RequirementPullRequest(
                requirement_id=requirement_id,
                project_id=project.id,
                source_branch=source_branch,
                target_branch=target_branch,
                pr_iid=result.get("iid"),
                pr_url=result.get("web_url"),
                status=PullRequestStatus.OPEN,
                create_time=get_utc_now()
            )
            db.add(pr_record)
            db.flush()  # 获取自增ID

            merge_requests_results.append({
                "target_branch": target_branch,
                "status": "success",
                "url": result.get("web_url"),
                "iid": result.get("iid"),
                "db_record_id": pr_record.id
            })
        except Exception as e:
            logger.error(f"[GitLab] 创建合并请求失败: {str(e)}")
            merge_requests_results.append({
                "target_branch": target_branch,
                "status": "error",
                "error": str(e)
            })

    # 提交所有数据库更改
    db.commit()

    # 发送企业微信通知
    try:
        project = db.query(Project).filter(Project.id == db_requirement.project_id).first()
        if project:
            # 获取开发人员ID列表
            developer_records = db.query(User).join(
                RequirementAssignment,
                User.id == RequirementAssignment.user_id
            ).filter(
                RequirementAssignment.requirement_id == db_requirement.id
            ).all()
            developer_ids = [dev.id for dev in developer_records]

            # 获取测试人员ID列表
            tester_records = db.query(User).join(
                project_testers_table,
                User.id == project_testers_table.c.user_id
            ).filter(
                project_testers_table.c.project_id == project.id
            ).all()
            tester_ids = [tester.id for tester in tester_records]

            # 通知开发人员和测试人员
            notify_user_ids = developer_ids + tester_ids
            if notify_user_ids:
                NotificationService.send_requirement_status_change_notification(
                    db=db,
                    requirement=db_requirement,
                    operator=current_user,
                    project=project,
                    action="通过验证",
                    new_status="已完成"
                )
    except Exception as e:
        logger.error(f"发送企业微信通知失败: {str(e)}")

    # 返回结果，包括合并请求的创建结果
    return {
        "message": "需求验证已通过，状态已更新为已完成",
        "merge_requests": merge_requests_results
    }

# 管理员驳回验证
@router.post("/requirements/{requirement_id}/reject-validation", status_code=status.HTTP_200_OK)
def reject_validation(
    requirement_id: int,
    reason: dict = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    管理员驳回验证，将需求状态更新为测试中
    """
    # 检查用户权限
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以执行此操作"
        )

    # 验证驳回理由
    if not reason.get("reason"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="驳回理由不能为空"
        )

    # 查询需求
    db_requirement = db.query(RequirementModel).filter(RequirementModel.id == requirement_id).first()
    if not db_requirement:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="需求不存在")

    # 检查需求状态
    if db_requirement.status != RequirementStatus.VALIDATING:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"只有验证中状态的需求可以驳回验证，当前状态: {db_requirement.status.value}"
        )

    # 获取当前状态
    _ = db_requirement.status  # 仅用于记录，不使用

    # 更新需求状态
    db_requirement.status = RequirementStatus.TESTING
    db_requirement.update_time = get_utc_now()

    # 记录状态变更
    history = RequirementHistory(
        requirement_id=requirement_id,
        current_status_code=RequirementStatus.TESTING.name,
        action_code=RequirementAction.REJECT_TEST.name,  # 使用驳回测试作为动作
        user_id=current_user.id,
        remark=reason.get("reason")
    )

    db.add(history)
    db.commit()

    # 发送企业微信通知
    try:
        project = db.query(Project).filter(Project.id == db_requirement.project_id).first()
        if project:
            NotificationService.send_requirement_status_change_notification(
                db=db,
                requirement=db_requirement,
                operator=current_user,
                project=project,
                action="驳回验证",
                new_status="测试中",
                reason=reason.get("reason")
            )
    except Exception as e:
        logger.error(f"发送企业微信通知失败: {str(e)}")

    return {"message": "需求验证已驳回，状态已更新为测试中"}

@router.get("/todos", response_model=List[dict])
def get_todos(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取当前用户的待办事项
    """
    logger.info(f"当前用户ID: {current_user.id}")
    query = db.query(
        Project.id.label('project_id'),
        Project.name.label('project_name'),
        RequirementModel.id.label('requirement_id'),
        RequirementModel.title.label('requirements_title'),
        RequirementModel.project_id.label('requirements_project_id'),
        RequirementModel.requirement_code.label('requirements_requirement_code'),
        RequirementModel.priority.label('requirements_priority'),
        RequirementModel.type.label('requirements_type'),
        RequirementModel.status.label('requirements_status'),
        RequirementModel.start_date.label('requirements_start_date'),
        RequirementModel.end_date.label('requirements_end_date'),
        func.group_concat(RequirementBranch.branch_name).label('branches'),
        func.group_concat(RequirementBranch.is_main).label('branch_is_main'),
        func.group_concat(RequirementAssignment.user_id).label('assignments'),
        func.group_concat(project_testers_table.c.user_id).label('testers')
    ).join(
        RequirementModel, Project.id == RequirementModel.project_id, isouter=True
    ).join(
        User, RequirementModel.submitter_id == User.id, isouter=True
    ).join(
        RequirementBranch, RequirementModel.id == RequirementBranch.requirement_id, isouter=True
    ).join(
        project_testers_table, Project.id == project_testers_table.c.project_id, isouter=True
    ).join(
        RequirementAssignment, RequirementModel.id == RequirementAssignment.requirement_id
    ).filter(
        ((RequirementAssignment.user_id == current_user.id) | (project_testers_table.c.user_id == current_user.id)) &
        (RequirementModel.status != 'COMPLETED')
    ).group_by(
        Project.id,
        Project.name,
        RequirementModel.id,
        RequirementModel.title,
        RequirementModel.project_id,
        RequirementModel.requirement_code,
        RequirementModel.priority,
        RequirementModel.type,
        RequirementModel.status,
        RequirementModel.start_date,
        RequirementModel.end_date,
        RequirementModel.submit_time,
        RequirementModel.submitter_id,
        User.name
    )

    # 输出完整SQL语句
    sql = str(query)
    logger.info(f"执行的SQL查询: {sql}")

    results = query.all()

    # 构造响应数据
    todos = []
    for row in results:
        todos.append({
            "projectId": row.project_id,
            "projectName": row.project_name,
            "requirementId": row.requirement_id,
            "requirementName": row.requirements_title,
            "requirementCode": row.requirements_requirement_code,
            "priority": row.requirements_priority,
            "type": row.requirements_type,
            "status": row.requirements_status,
            "startDate": row.requirements_start_date,
            "endDate": row.requirements_end_date,
            "branches": row.branches.split(',') if row.branches else [],
            "branch_is_main": [bool(int(x)) for x in row.branch_is_main.split(',')] if row.branch_is_main else [],
            "assignments": row.assignments.split(',') if row.assignments else [],
            "testers": row.testers.split(',') if row.testers else []
        })

    return todos

@router.get("/requirements/{requirement_id}/pull-requests", response_model=List[dict])
def get_requirement_pull_requests(
    requirement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取需求相关的PR信息
    """
    # 检查需求是否存在
    requirement = db.query(RequirementModel).filter(RequirementModel.id == requirement_id).first()
    if not requirement:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="需求不存在")

    # 非管理员检查权限
    if not current_user.is_admin:
        is_assigned = db.query(RequirementAssignment).filter(
            RequirementAssignment.requirement_id == requirement_id,
            RequirementAssignment.user_id == current_user.id
        ).first()

        if not is_assigned:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限查看此需求的PR信息"
            )

    # 查询PR信息
    pull_requests = db.query(RequirementPullRequest).filter(
        RequirementPullRequest.requirement_id == requirement_id
    ).all()

    # 构造响应数据
    result = []
    for pr in pull_requests:
        result.append({
            "id": pr.id,
            "requirement_id": pr.requirement_id,
            "project_id": pr.project_id,
            "source_branch": pr.source_branch,
            "target_branch": pr.target_branch,
            "pr_iid": pr.pr_iid,
            "pr_url": pr.pr_url,
            "status": pr.status,
            "create_time": pr.create_time,
            "update_time": pr.update_time
        })

    return result

@router.get("/requirements-list")
def get_requirements_list(
    type: Optional[str] = None,
    priority: Optional[str] = None,
    status: Optional[str] = None,
    uncomplete: Optional[bool] = None,
    page: int = Query(1, ge=1, description="页码，从1开始"),
    pageSize: int = Query(10, ge=1, le=100, description="每页条数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取需求列表，管理员可以看到所有需求，普通用户只能看到自己参与的需求
    支持按需求类型、优先级、状态筛选
    支持分页
    支持获取所有未完成的需求（uncomplete=True）
    """
    logger.info(f"需求列表查询 - 当前用户ID: {current_user.id}, 是否管理员: {current_user.is_admin}")
    logger.info(f"筛选条件 - 类型: {type}, 优先级: {priority}, 状态: {status}, 未完成: {uncomplete}")
    logger.info(f"分页参数 - 页码: {page}, 每页条数: {pageSize}")

    # 创建User表别名，用于不同角色的用户查询
    SubmitterUser = aliased(User)
    AssignmentUser = aliased(User)

    # 基础查询
    base_query = db.query(
        Project.id.label('project_id'),
        Project.name.label('project_name'),
        RequirementModel.id.label('requirement_id'),
        RequirementModel.title.label('requirements_title'),
        RequirementModel.project_id.label('requirements_project_id'),
        RequirementModel.requirement_code.label('requirements_requirement_code'),
        RequirementModel.priority.label('requirements_priority'),
        RequirementModel.type.label('requirements_type'),
        RequirementModel.status.label('requirements_status'),
        RequirementModel.start_date.label('requirements_start_date'),
        RequirementModel.end_date.label('requirements_end_date'),
        RequirementModel.git_branch.label('requirements_git_branch'),
        RequirementModel.submit_time.label('requirements_submit_time'),
        RequirementModel.submitter_id.label('requirements_submitter_id'),
        SubmitterUser.name.label('submitter_name'),
        func.group_concat(RequirementBranch.branch_name).label('branches'),
        func.group_concat(RequirementBranch.is_main).label('branch_is_main')
    ).join(
        RequirementModel, Project.id == RequirementModel.project_id
    ).join(
        SubmitterUser, RequirementModel.submitter_id == SubmitterUser.id, isouter=True
    ).join(
        RequirementBranch, RequirementModel.id == RequirementBranch.requirement_id, isouter=True
    )

    # 非管理员只能看到自己参与的需求（作为开发人员或测试人员）
    if not current_user.is_admin:
        # 使用子查询检查用户是否为开发人员
        developer_subquery = db.query(RequirementAssignment.requirement_id).filter(
            RequirementAssignment.user_id == current_user.id
        ).subquery()

        # 使用子查询检查用户是否为测试人员
        tester_subquery = db.query(RequirementModel.id).join(
            project_testers_table, RequirementModel.project_id == project_testers_table.c.project_id
        ).filter(
            project_testers_table.c.user_id == current_user.id
        ).subquery()

        # 过滤条件：用户是开发人员或测试人员，且需求未完成
        base_query = base_query.filter(
            ((RequirementModel.id.in_(developer_subquery)) |
             (RequirementModel.id.in_(tester_subquery))) &
            (RequirementModel.status != 'COMPLETED')
        )

    # 添加筛选条件
    if type:
        base_query = base_query.filter(RequirementModel.type == type)
    if priority:
        base_query = base_query.filter(RequirementModel.priority == priority)
    if status:
        base_query = base_query.filter(RequirementModel.status == status)
    elif uncomplete:
        # 如果指定了uncomplete参数，筛选出所有未完成的需求
        base_query = base_query.filter(RequirementModel.status != RequirementStatus.COMPLETED)

    # 分组
    base_query = base_query.group_by(
        Project.id,
        Project.name,
        RequirementModel.id,
        RequirementModel.title,
        RequirementModel.project_id,
        RequirementModel.requirement_code,
        RequirementModel.priority,
        RequirementModel.type,
        RequirementModel.status,
        RequirementModel.start_date,
        RequirementModel.end_date,
        RequirementModel.git_branch,
        RequirementModel.submit_time,
        RequirementModel.submitter_id,
        SubmitterUser.name
    )

    # 排序：优先级、计划完成时间（日期倒序）
    base_query = base_query.order_by(
        RequirementModel.priority,
        desc(RequirementModel.end_date)
    )

    # 计算总数
    # 由于SQLite不支持在GROUP BY查询上使用COUNT OVER()，我们需要使用子查询
    # 使用兼容不同SQLAlchemy版本的方式
    try:
        # 尝试新版本的API
        from sqlalchemy import select
        count_query = select(func.count()).select_from(base_query.subquery())
    except:
        try:
            # 尝试新版本的with_only_columns用法
            count_query = base_query.statement.with_only_columns(func.count()).order_by(None)
        except:
            # 回退到旧版本的用法
            count_query = base_query.statement.with_only_columns([func.count()]).order_by(None)

    total_count = db.execute(count_query).scalar()

    # 分页
    skip = (page - 1) * pageSize
    query = base_query.offset(skip).limit(pageSize)

    # 输出完整SQL语句
    sql = str(query)
    print(f"需求列表查询SQL: {sql}")

    results = query.all()

    # 批量查询开发人员和测试人员信息
    requirement_ids = list(set([row.requirement_id for row in results]))
    project_ids = list(set([row.project_id for row in results]))

    # 初始化字典
    requirement_developers_dict = {}
    project_testers_dict = {}

    if requirement_ids:
        # 查询所有相关需求的开发人员
        developers_query = db.query(
            RequirementAssignment.requirement_id,
            User.id,
            User.name
        ).join(
            User, RequirementAssignment.user_id == User.id
        ).filter(
            RequirementAssignment.requirement_id.in_(requirement_ids)
        ).all()

        # 按requirement_id组织开发人员数据
        for dev_row in developers_query:
            requirement_id = dev_row.requirement_id
            if requirement_id not in requirement_developers_dict:
                requirement_developers_dict[requirement_id] = []
            requirement_developers_dict[requirement_id].append({
                "id": dev_row.id,
                "name": dev_row.name
            })

    if project_ids:
        # 查询所有相关项目的测试人员
        testers_query = db.query(
            project_testers_table.c.project_id,
            User.id,
            User.name
        ).join(
            User, project_testers_table.c.user_id == User.id
        ).filter(
            project_testers_table.c.project_id.in_(project_ids)
        ).all()

        # 按project_id组织测试人员数据
        for tester_row in testers_query:
            project_id = tester_row.project_id
            if project_id not in project_testers_dict:
                project_testers_dict[project_id] = []
            project_testers_dict[project_id].append({
                "id": tester_row.id,
                "name": tester_row.name
            })

    # 构造响应数据
    requirements_list = []
    for row in results:
        # 从预查询的数据中获取开发人员信息
        developers = requirement_developers_dict.get(row.requirement_id, [])

        # 从预查询的数据中获取测试人员信息
        testers = project_testers_dict.get(row.project_id, [])

        # 将数据库中的英文状态码转换为中文显示
        status_display = row.requirements_status
        if hasattr(RequirementStatus, row.requirements_status):
            status_enum = getattr(RequirementStatus, row.requirements_status)
            status_display = status_enum.value

        requirements_list.append({
            "projectId": row.project_id,
            "projectName": row.project_name,
            "requirementId": row.requirement_id,
            "requirementName": row.requirements_title,
            "requirementCode": row.requirements_requirement_code,
            "priority": row.requirements_priority,
            "type": row.requirements_type,
            "status": status_display,
            "startDate": row.requirements_start_date,
            "endDate": row.requirements_end_date,
            "gitBranch": row.requirements_git_branch,
            "submitTime": row.requirements_submit_time,
            "submitterId": row.requirements_submitter_id,
            "submitterName": row.submitter_name,
            "branches": row.branches.split(',') if row.branches else [],
            "branch_is_main": [bool(int(x)) for x in row.branch_is_main.split(',')] if row.branch_is_main else [],
            "developers": developers,
            "testers": testers
        })

    # 返回分页结果
    return {
        "items": requirements_list,
        "total": total_count,
        "page": page,
        "pageSize": pageSize
    }